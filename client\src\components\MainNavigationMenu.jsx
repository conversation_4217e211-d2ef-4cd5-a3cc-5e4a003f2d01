import React, { useState, useEffect } from 'react'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "./ui/navigation-menu"
import { cn } from "@/lib/utils"

const MainNavigationMenu = () => {
  const [isHovered, setIsHovered] = useState(false)



  return (
    <div
      className="fixed top-16 left-0 w-full z-40 transition-all duration-300 !bg-transparent navbar-transparent"
      style={{
        backgroundColor: 'transparent !important',
        background: 'transparent !important',
        backgroundImage: 'none !important'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between w-full py-2">
          {/* Left Side - First 3 items */}
          <NavigationMenu>
            <NavigationMenuList className="flex items-center gap-1">
              <NavigationMenuItem>
                <NavigationMenuTrigger
                  className={`navbar-trigger-transparent text-white transition-colors duration-300 drop-shadow-lg`}
                  style={{
                    textShadow: '0 2px 4px rgba(0,0,0,0.5)',
                    backgroundColor: 'transparent !important',
                    background: 'transparent !important',
                    backgroundImage: 'none !important'
                  }}
                >
                  All categories
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                    <li className="row-span-3">
                      <NavigationMenuLink asChild>
                        <a
                          className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                          href="/"
                        >
                          <div className="mb-2 mt-4 text-lg font-medium">
                            Featured Categories
                          </div>
                          <p className="text-sm leading-tight text-muted-foreground">
                            Discover our most popular product categories
                          </p>
                        </a>
                      </NavigationMenuLink>
                    </li>
                    <ListItem href="/electronics" title="Electronics">
                      Smartphones, laptops, and gadgets
                    </ListItem>
                    <ListItem href="/fashion" title="Fashion">
                      Clothing, shoes, and accessories
                    </ListItem>
                    <ListItem href="/home" title="Home & Garden">
                      Furniture, decor, and tools
                    </ListItem>
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <NavigationMenuTrigger
                  className={`navbar-trigger-transparent text-white transition-colors duration-300 drop-shadow-lg`}
                  style={{
                    textShadow: '0 2px 4px rgba(0,0,0,0.5)',
                    backgroundColor: 'transparent !important',
                    background: 'transparent !important',
                    backgroundImage: 'none !important'
                  }}
                >
                  Featured selections
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                    <ListItem title="Smartphones" href="/electronics/smartphones">
                      Latest mobile phones and accessories
                    </ListItem>
                    <ListItem title="Laptops" href="/electronics/laptops">
                      Computers and laptop accessories
                    </ListItem>
                    {/* ... other items */}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <NavigationMenuTrigger
                  className={`navbar-trigger-transparent text-white transition-colors duration-300 drop-shadow-lg`}
                  style={{
                    textShadow: '0 2px 4px rgba(0,0,0,0.5)',
                    backgroundColor: 'transparent !important',
                    background: 'transparent !important',
                    backgroundImage: 'none !important'
                  }}
                >
                  Daily protections
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                    <ListItem title="Men's Clothing" href="/fashion/mens">
                      Shirts, pants, suits, and casual wear
                    </ListItem>
                    <ListItem title="Women's Clothing" href="/fashion/womens">
                      Dresses, tops, bottoms, and formal wear
                    </ListItem>
                    {/* ... other items */}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>

          {/* Right Side - Remaining items */}
          <NavigationMenu>
            <NavigationMenuList className="flex items-center gap-1">
              <NavigationMenuItem>
                <NavigationMenuTrigger
                  className={`navbar-trigger-transparent text-white transition-colors duration-300 drop-shadow-lg`}
                  style={{
                    textShadow: '0 2px 4px rgba(0,0,0,0.5)',
                    backgroundColor: 'transparent !important',
                    background: 'transparent !important',
                    backgroundImage: 'none !important'
                  }}
                >
                  A sourcing agent
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                    <ListItem title="Furniture" href="/home/<USER>">
                      Sofas, tables, chairs, and bedroom sets
                    </ListItem>
                    <ListItem title="Kitchen" href="/home/<USER>">
                      Appliances, cookware, and utensils
                    </ListItem>
                    {/* ... other items */}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <NavigationMenuTrigger
                  className={`navbar-trigger-transparent text-white transition-colors duration-300 drop-shadow-lg`}
                  style={{
                    textShadow: '0 2px 4px rgba(0,0,0,0.5)',
                    backgroundColor: 'transparent !important',
                    background: 'transparent !important',
                    backgroundImage: 'none !important'
                  }}
                >
                  Buyer & Business
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                    <ListItem title="Fitness Equipment" href="/sports/fitness">
                      Gym equipment and workout gear
                    </ListItem>
                    <ListItem title="Outdoor Gear" href="/sports/outdoor">
                      Camping, hiking, and adventure equipment
                    </ListItem>
                    {/* ... other items */}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <NavigationMenuLink
                  href="/help-center"
                  className={cn("group inline-flex h-9 w-max items-center justify-center rounded-md bg-transparent px-4 py-2 text-sm font-medium transition-colors focus:outline-none disabled:pointer-events-none disabled:opacity-50", 'text-white hover:text-gray-200')}
                  style={{ textShadow: '0 2px 4px rgba(0,0,0,0.5)' }}
                >
                  Help Center
                </NavigationMenuLink>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <NavigationMenuLink
                  href="/ship-to"
                  className={cn("group inline-flex h-9 w-max items-center justify-center rounded-md bg-transparent px-4 py-2 text-sm font-medium transition-colors focus:outline-none disabled:pointer-events-none disabled:opacity-50", 'text-white hover:text-gray-200')}
                  style={{ textShadow: '0 2px 4px rgba(0,0,0,0.5)' }}
                >
                  Ship to
                </NavigationMenuLink>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <NavigationMenuLink
                  href="/language"
                  className={cn("group inline-flex h-9 w-max items-center justify-center rounded-md bg-transparent px-4 py-2 text-sm font-medium transition-colors focus:outline-none disabled:pointer-events-none disabled:opacity-50", 'text-white hover:text-gray-200')}
                  style={{ textShadow: '0 2px 4px rgba(0,0,0,0.5)' }}
                >
                  EN-USD
                </NavigationMenuLink>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
        </div>
      </div>
    </div>
  )
}

const ListItem = React.forwardRef(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"

export default MainNavigationMenu
